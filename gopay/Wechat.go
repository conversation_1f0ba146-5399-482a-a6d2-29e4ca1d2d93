package gopay

import (
	"context"
	"fmt"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/wechat"
	"strconv"
	"time"
)

// SignType 加密方式
const SignType = wechat.SignType_MD5

// PayResult 统一返回参数格式
type PayResult map[string]interface{}

// Set 设置参数
func (r PayResult) Set(key string, value interface{}) PayResult {
	r[key] = value
	return r
}

type WePay struct {
	Client *wechat.Client
	Config *WeConfig
}

func NewWePay(config *WeConfig) *WePay {
	client := wechat.NewClient(config.AppId, config.MchId, config.ApiKey, true)
	return &WePay{
		Client: client,
		Config: config,
	}
}

func (p *WePay) commonBm() {
	bm := make(gopay.BodyMap)
	//设置随机字符串
	bm.Set("nonce_str", time.Now().Unix()).
		//设置加密方式
		Set("sign_type", SignType).
		//设置公共回掉地址 更改的时候自动覆盖
		Set("notify_url", p.Config.NotifyUrl)

	//设置子商户号
	if p.Config.SubMchId != "" {
		bm.Set("sub_mch_id", p.Config.SubMchId)
	}
}

func (p *WePay) check(bm *gopay.BodyMap) error {
	//验证订单号
	if bm.GetString("out_trade_no") == "" {
		return fmt.Errorf("请设置商户订单号")
	}
	//验证金额
	if bm.GetString("total_fee") == "" {
		return fmt.Errorf("请设置支付金额")
	}
	return nil
}

func (p *WePay) UnifiedPayCheck(rsp *wechat.UnifiedOrderResponse) error {
	if rsp.ReturnCode == "FAIL" {
		return fmt.Errorf(rsp.ReturnMsg)
	}
	if rsp.ResultCode == "FAIL" {
		return fmt.Errorf(rsp.ErrCodeDes)
	}
	return nil
}

func (p *WePay) App(ctx context.Context, bm *gopay.BodyMap) (*PayResult, error) {
	bm.Set("trade_type", wechat.TradeType_App)
	if err := p.check(bm); err != nil {
		return nil, err
	}

	//统一下单
	rsp, err := p.Client.UnifiedOrder(ctx, *bm)
	if err != nil {
		return nil, err
	}

	//验证错误
	if err := p.UnifiedPayCheck(rsp); err != nil {
		return nil, err
	}

	//组装app发起支付需要的数据
	result := new(PayResult)
	timeStamp := strconv.FormatInt(time.Now().Unix(), 10)
	paySign := wechat.GetAppPaySign(p.Config.AppId, p.Config.SubMchId, rsp.NonceStr, rsp.PrepayId, SignType, timeStamp, p.Config.ApiKey)
	result.Set("appid", p.Config.AppId).
		Set("partnerid", p.Config.SubMchId).
		Set("nonceStr", rsp.NonceStr).
		Set("prepayId", rsp.PrepayId).
		Set("signType", SignType).
		Set("timeStamp", timeStamp).
		Set("apiKey", p.Config.ApiKey).
		Set("sign", paySign)

	return result, nil
}
