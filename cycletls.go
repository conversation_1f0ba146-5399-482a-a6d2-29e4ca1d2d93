package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/Danny-Dasilva/CycleTLS/cycletls"
)

// RequestPayload 定义客户端发送的请求结构
type RequestPayload struct {
	URL     string            `json:"url"`
	Method  string            `json:"method"`
	Headers map[string]string `json:"headers"`
	Cookies []struct {
		Name  string `json:"name"`
		Value string `json:"value"`
	} `json:"cookies"`
	Body  string `json:"body"`
	Proxy string `json:"proxy"`
	JA3   string `json:"ja3"`
}

// ResponsePayload 定义返回给客户端的响应结构
type ResponsePayload struct {
	Status     int               `json:"status"`
	Headers    map[string]string `json:"headers"`
	Body       string            `json:"body"`
	Error      string            `json:"error,omitempty"`
	StatusCode int               `json:"statusCode"`
}

func main() {
	http.HandleFunc("/forward", forwardHandler)
	fmt.Println("转发服务已启动，监听端口 8080...")
	log.Fatal(http.ListenAndServe(":8080", nil))
}

func forwardHandler(w http.ResponseWriter, r *http.Request) {
	// 设置响应头
	w.Header().Set("Content-Type", "application/json")

	// 只接受 POST 请求
	if r.Method != http.MethodPost {
		responseError(w, "只支持 POST 请求", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体
	var payload RequestPayload
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		responseError(w, "无法解析请求体: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 验证必要参数
	if payload.URL == "" {
		responseError(w, "URL 不能为空", http.StatusBadRequest)
		return
	}

	// 设置默认方法为 GET
	if payload.Method == "" {
		payload.Method = "GET"
	} else {
		payload.Method = strings.ToUpper(payload.Method)
	}

	// 初始化 CycleTLS 客户端
	client := cycletls.Init()

	// 准备请求选项
	options := cycletls.Options{
		URL:     payload.URL,
		Method:  payload.Method,
		Headers: payload.Headers,
		Body:    payload.Body,
		Proxy:   payload.Proxy,
		Ja3:     payload.JA3,
	}

	// 添加 Cookies
	if len(payload.Cookies) > 0 {
		var cookieStrs []string
		for _, cookie := range payload.Cookies {
			cookieStrs = append(cookieStrs, fmt.Sprintf("%s=%s", cookie.Name, cookie.Value))
		}
		if options.Headers == nil {
			options.Headers = make(map[string]string)
		}
		options.Headers["Cookie"] = strings.Join(cookieStrs, "; ")
	}

	// 发送请求
	response, err := client.Do(payload.URL, options, payload.Method)
	if err != nil {
		responseError(w, "请求失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 处理响应
	respPayload := ResponsePayload{
		Status:  response.Status,
		Body:    response.Body,
		Headers: make(map[string]string),
	}

	// 转换响应头
	for k, v := range response.Headers {
		if len(v) > 0 {
			respPayload.Headers[k] = v[0]
		}
	}

	// 返回响应
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(respPayload)
}

func responseError(w http.ResponseWriter, message string, statusCode int) {
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(ResponsePayload{
		Error:      message,
		StatusCode: statusCode,
	})
}
